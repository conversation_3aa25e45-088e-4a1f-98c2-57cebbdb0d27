<template>
  <view class="add-agent-page">
    <scroll-view class="content" scroll-y>
      <!-- 基本信息 -->
      <view class="section">
        <view class="section-header">
          <van-icon name="info-o" />
          <text>基本信息</text>
        </view>

        <view class="form-item">
          <view class="label required">代理商名称</view>
          <van-field
            v-model="formData.agentName"
            placeholder="代理商名称"
            border="{{ false }}"
          />
        </view>

        <view class="form-item">
          <view class="label">代理商编号</view>
          <van-field
            v-model="formData.agentCode"
            placeholder="SHG1651651631G"
            border="{{ false }}"
            disabled
          />
        </view>

        <view class="form-item">
          <view class="label required">代理商名称</view>
          <view class="radio-group">
            <van-radio-group v-model="formData.agentType">
              <van-radio name="enterprise">企业</van-radio>
              <van-radio name="individual">个人</van-radio>
            </van-radio-group>
          </view>
        </view>

        <view class="form-item">
          <view class="label">联系电话</view>
          <van-field
            v-model="formData.phone"
            placeholder="联系电话"
            border="{{ false }}"
            type="number"
          />
        </view>

        <view class="form-item">
          <view class="label required">企业统一信用代码</view>
          <van-field
            v-model="formData.creditCode"
            placeholder="企业统一信用代码"
            border="{{ false }}"
          />
        </view>

        <view class="form-item">
          <view class="label required">所在省份</view>
          <van-field
            v-model="formData.provinceName"
            placeholder="请选择"
            border="{{ false }}"
            readonly
            is-link
            @click="showProvincePicker = true"
          />
        </view>

        <view class="form-item">
          <view class="label required">所在城市</view>
          <van-field
            v-model="formData.cityName"
            placeholder="请选择"
            border="{{ false }}"
            readonly
            is-link
            @click="showCityPicker = true"
          />
        </view>

        <view class="form-item">
          <view class="label">详细地址</view>
          <van-field
            v-model="formData.address"
            placeholder="详细地址"
            border="{{ false }}"
            type="textarea"
            autosize
          />
        </view>

        <view class="form-item">
          <view class="label">主要覆盖区域</view>
          <view class="area-row">
            <van-field
              v-model="formData.coverProvinceName"
              placeholder="请选择省份"
              border="{{ false }}"
              readonly
              is-link
              @click="showCoverProvincePicker = true"
            />
            <van-field
              v-model="formData.coverCityName"
              placeholder="请选择城市"
              border="{{ false }}"
              readonly
              is-link
              @click="showCoverCityPicker = true"
            />
          </view>
        </view>

        <view class="form-item">
          <view class="label">团队人员</view>
          <van-field
            v-model="formData.teamMember"
            placeholder="请选择"
            border="{{ false }}"
            readonly
            is-link
            @click="showTeamPicker = true"
          />
        </view>
      </view>

      <!-- 意向产品 -->
      <view class="section">
        <view class="section-header">
          <van-icon name="shopping-cart-o" />
          <text>意向产品</text>
          <view class="add-btn" @click="addProduct">
            <van-icon name="plus" />
            <text>添加</text>
          </view>
        </view>

        <view
          v-for="(item, index) in formData.products"
          :key="index"
          class="product-item"
        >
          <view class="product-header">
            <text>{{ item.productName }}</text>
            <van-icon name="cross" @click="removeProduct(index)" />
          </view>
          <view class="product-info">
            <text>代理经验：{{ item.agentExperience }}</text>
          </view>
          <view class="product-info">
            <text>竞品经验：{{ item.competitorExperience }}</text>
          </view>
          <view class="product-info">
            <text>竞品名称：{{ item.competitorName }}</text>
          </view>
        </view>
      </view>

      <!-- 意向终端 -->
      <view class="section">
        <view class="section-header">
          <van-icon name="shop-o" />
          <text>意向终端</text>
          <view class="add-btn" @click="addTerminal">
            <van-icon name="plus" />
            <text>添加</text>
          </view>
        </view>

        <view
          v-for="(item, index) in formData.terminals"
          :key="index"
          class="terminal-item"
        >
          <view class="terminal-header">
            <text>{{ item.terminalName }}</text>
            <van-icon name="cross" @click="removeTerminal(index)" />
          </view>
          <view class="terminal-info">
            <text>科室：{{ item.department }}</text>
          </view>
          <view class="terminal-info">
            <text>代理经验：{{ item.agentExperience }}</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <van-button plain @click="cancel">取消</van-button>
      <van-button type="primary" @click="save">保存</van-button>
    </view>

    <!-- 省份选择器 -->
    <van-popup
      v-model="showProvincePicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="provinceColumns"
        @confirm="onProvinceConfirm"
        @cancel="showProvincePicker = false"
        show-toolbar
        title="选择省份"
      />
    </van-popup>

    <!-- 城市选择器 -->
    <van-popup
      v-model="showCityPicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="cityColumns"
        @confirm="onCityConfirm"
        @cancel="showCityPicker = false"
        show-toolbar
        title="选择城市"
      />
    </van-popup>

    <!-- 覆盖省份选择器 -->
    <van-popup
      v-model="showCoverProvincePicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="provinceColumns"
        @confirm="onCoverProvinceConfirm"
        @cancel="showCoverProvincePicker = false"
        show-toolbar
        title="选择覆盖省份"
      />
    </van-popup>

    <!-- 覆盖城市选择器 -->
    <van-popup
      v-model="showCoverCityPicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="coverCityColumns"
        @confirm="onCoverCityConfirm"
        @cancel="showCoverCityPicker = false"
        show-toolbar
        title="选择覆盖城市"
      />
    </van-popup>

    <!-- 团队人员选择器 -->
    <van-popup
      v-model="showTeamPicker"
      position="bottom"
      custom-style="height: 40%"
    >
      <van-picker
        :columns="teamColumns"
        @confirm="onTeamConfirm"
        @cancel="showTeamPicker = false"
        show-toolbar
        title="选择团队人员"
      />
    </van-popup>

    <!-- 意向产品弹窗 -->
    <van-popup
      v-model="showProductPopup"
      position="center"
      custom-style="width: 80%; border-radius: 16rpx;"
    >
      <view class="popup-content">
        <view class="popup-title">选择意向产品</view>

        <view class="form-item">
          <view class="label required">产品名称</view>
          <van-field
            v-model="productForm.productName"
            placeholder="产品名称"
            border="{{ false }}"
          />
        </view>

        <view class="form-item">
          <view class="label required">代理经验</view>
          <view class="radio-group">
            <van-radio-group v-model="productForm.agentExperience">
              <van-radio name="enterprise">企业</van-radio>
              <van-radio name="individual">个人</van-radio>
            </van-radio-group>
          </view>
        </view>

        <view class="form-item">
          <view class="label required">竞品经验</view>
          <view class="radio-group">
            <van-radio-group v-model="productForm.competitorExperience">
              <van-radio name="enterprise">企业</van-radio>
              <van-radio name="individual">个人</van-radio>
            </van-radio-group>
          </view>
        </view>

        <view class="form-item">
          <view class="label required">竞品名称</view>
          <van-field
            v-model="productForm.competitorName"
            placeholder="竞品名称"
            border="{{ false }}"
          />
        </view>

        <view class="popup-actions">
          <van-button plain @click="cancelProduct">取消</van-button>
          <van-button type="primary" @click="confirmProduct">确定</van-button>
        </view>
      </view>
    </van-popup>

    <!-- 意向终端弹窗 -->
    <van-popup
      v-model="showTerminalPopup"
      position="center"
      custom-style="width: 80%; border-radius: 16rpx;"
    >
      <view class="popup-content">
        <view class="popup-title">选择意向终端</view>

        <view class="form-item">
          <view class="label required">终端名称</view>
          <van-field
            v-model="terminalForm.terminalName"
            placeholder="终端名称"
            border="{{ false }}"
          />
        </view>

        <view class="form-item">
          <view class="label">科室</view>
          <van-field
            v-model="terminalForm.department"
            placeholder="科室"
            border="{{ false }}"
          />
        </view>

        <view class="form-item">
          <view class="label">代理经验</view>
          <van-field
            v-model="terminalForm.agentExperience"
            placeholder="代理经验"
            border="{{ false }}"
          />
        </view>

        <view class="popup-actions">
          <van-button plain @click="cancelTerminal">取消</van-button>
          <van-button type="primary" @click="confirmTerminal">确定</van-button>
        </view>
      </view>
    </van-popup>
  </view>
</template>

<script>
import {
  addAgent,
  editAgent,
  getAgentDetail,
} from "@/common/api/agentDA/index.js";
import { getProvinceList, getCityList } from "@/common/data/area.js";

export default {
  data() {
    return {
      // 页面模式：add-新增，edit-编辑
      mode: "add",
      agentId: "",

      // 表单数据
      formData: {
        agentName: "",
        agentCode: "",
        agentType: "enterprise",
        phone: "",
        creditCode: "",
        provinceCode: "",
        provinceName: "",
        cityCode: "",
        cityName: "",
        address: "",
        coverProvinceCode: "",
        coverProvinceName: "",
        coverCityCode: "",
        coverCityName: "",
        teamMember: "",
        products: [],
        terminals: [],
      },

      // 选择器显示状态
      showProvincePicker: false,
      showCityPicker: false,
      showCoverProvincePicker: false,
      showCoverCityPicker: false,
      showTeamPicker: false,
      showProductPopup: false,
      showTerminalPopup: false,

      // 选择器数据
      provinceColumns: [],
      cityColumns: [],
      coverCityColumns: [],
      teamColumns: [
        { text: "曹玲", value: "曹玲" },
        { text: "张三", value: "张三" },
        { text: "李四", value: "李四" },
      ],

      // 产品表单
      productForm: {
        productName: "",
        agentExperience: "enterprise",
        competitorExperience: "enterprise",
        competitorName: "",
      },

      // 终端表单
      terminalForm: {
        terminalName: "",
        department: "",
        agentExperience: "",
      },
    };
  },

  onLoad(options) {
    if (options.id) {
      this.agentId = options.id;
      this.mode = options.mode || "edit";
      this.loadAgentDetail();
    }

    this.initProvinceData();
    this.generateAgentCode();
  },

  methods: {
    // 初始化省份数据
    initProvinceData() {
      const provinces = getProvinceList();
      this.provinceColumns = provinces.map((item) => ({
        text: item.name,
        value: item.code,
      }));
    },

    // 生成代理商编号
    generateAgentCode() {
      if (this.mode === "add") {
        const timestamp = Date.now().toString().slice(-10);
        this.formData.agentCode = `SHG${timestamp}`;
      }
    },

    // 加载代理商详情
    async loadAgentDetail() {
      try {
        const res = await getAgentDetail(this.agentId);
        if (res.code === 200) {
          this.formData = { ...this.formData, ...res.data };

          // 更新城市数据
          if (this.formData.provinceCode) {
            this.updateCityColumns(this.formData.provinceCode);
          }
          if (this.formData.coverProvinceCode) {
            this.updateCoverCityColumns(this.formData.coverProvinceCode);
          }
        }
      } catch (error) {
        console.error("加载代理商详情失败:", error);
      }
    },

    // 省份选择确认
    onProvinceConfirm(value) {
      const selectedItem = this.provinceColumns.find(
        (item) => item.value === value
      );
      this.formData.provinceName = selectedItem ? selectedItem.text : "";
      this.formData.provinceCode = value;
      this.showProvincePicker = false;

      // 重置城市选择
      this.formData.cityName = "";
      this.formData.cityCode = "";

      // 更新城市数据
      this.updateCityColumns(value);
    },

    // 城市选择确认
    onCityConfirm(value) {
      const selectedItem = this.cityColumns.find(
        (item) => item.value === value
      );
      this.formData.cityName = selectedItem ? selectedItem.text : "";
      this.formData.cityCode = value;
      this.showCityPicker = false;
    },

    // 覆盖省份选择确认
    onCoverProvinceConfirm(value) {
      const selectedItem = this.provinceColumns.find(
        (item) => item.value === value
      );
      this.formData.coverProvinceName = selectedItem ? selectedItem.text : "";
      this.formData.coverProvinceCode = value;
      this.showCoverProvincePicker = false;

      // 重置覆盖城市选择
      this.formData.coverCityName = "";
      this.formData.coverCityCode = "";

      // 更新覆盖城市数据
      this.updateCoverCityColumns(value);
    },

    // 覆盖城市选择确认
    onCoverCityConfirm(value) {
      const selectedItem = this.coverCityColumns.find(
        (item) => item.value === value
      );
      this.formData.coverCityName = selectedItem ? selectedItem.text : "";
      this.formData.coverCityCode = value;
      this.showCoverCityPicker = false;
    },

    // 团队人员选择确认
    onTeamConfirm(value) {
      const selectedItem = this.teamColumns.find(
        (item) => item.value === value
      );
      this.formData.teamMember = selectedItem ? selectedItem.text : "";
      this.showTeamPicker = false;
    },

    // 更新城市列表
    updateCityColumns(provinceCode) {
      if (provinceCode) {
        const cities = getCityList(provinceCode);
        this.cityColumns = cities.map((item) => ({
          text: item.name,
          value: item.code,
        }));
      } else {
        this.cityColumns = [];
      }
    },

    // 更新覆盖城市列表
    updateCoverCityColumns(provinceCode) {
      if (provinceCode) {
        const cities = getCityList(provinceCode);
        this.coverCityColumns = cities.map((item) => ({
          text: item.name,
          value: item.code,
        }));
      } else {
        this.coverCityColumns = [];
      }
    },

    // 添加产品
    addProduct() {
      this.productForm = {
        productName: "",
        agentExperience: "enterprise",
        competitorExperience: "enterprise",
        competitorName: "",
      };
      this.showProductPopup = true;
    },

    // 确认添加产品
    confirmProduct() {
      if (!this.productForm.productName) {
        uni.showToast({
          title: "请输入产品名称",
          icon: "none",
        });
        return;
      }

      this.formData.products.push({ ...this.productForm });
      this.showProductPopup = false;
    },

    // 取消添加产品
    cancelProduct() {
      this.showProductPopup = false;
    },

    // 删除产品
    removeProduct(index) {
      this.formData.products.splice(index, 1);
    },

    // 添加终端
    addTerminal() {
      this.terminalForm = {
        terminalName: "",
        department: "",
        agentExperience: "",
      };
      this.showTerminalPopup = true;
    },

    // 确认添加终端
    confirmTerminal() {
      if (!this.terminalForm.terminalName) {
        uni.showToast({
          title: "请输入终端名称",
          icon: "none",
        });
        return;
      }

      this.formData.terminals.push({ ...this.terminalForm });
      this.showTerminalPopup = false;
    },

    // 取消添加终端
    cancelTerminal() {
      this.showTerminalPopup = false;
    },

    // 删除终端
    removeTerminal(index) {
      this.formData.terminals.splice(index, 1);
    },

    // 表单验证
    validateForm() {
      if (!this.formData.agentName) {
        uni.showToast({
          title: "请输入代理商名称",
          icon: "none",
        });
        return false;
      }

      if (!this.formData.creditCode) {
        uni.showToast({
          title: "请输入企业统一信用代码",
          icon: "none",
        });
        return false;
      }

      if (!this.formData.provinceCode) {
        uni.showToast({
          title: "请选择所在省份",
          icon: "none",
        });
        return false;
      }

      if (!this.formData.cityCode) {
        uni.showToast({
          title: "请选择所在城市",
          icon: "none",
        });
        return false;
      }

      return true;
    },

    // 保存
    async save() {
      if (!this.validateForm()) return;

      try {
        uni.showLoading({
          title: "保存中...",
        });

        let res;
        if (this.mode === "add") {
          res = await addAgent(this.formData);
        } else {
          res = await editAgent({ ...this.formData, id: this.agentId });
        }

        uni.hideLoading();

        if (res.code === 200) {
          uni.showToast({
            title: "保存成功",
            icon: "success",
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.message || "保存失败",
            icon: "none",
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error("保存失败:", error);
        uni.showToast({
          title: "网络错误",
          icon: "none",
        });
      }
    },

    // 取消
    cancel() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.add-agent-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.content {
  flex: 1;
  padding: 20rpx;
}

.section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-bottom: 1rpx solid #f0f0f0;

  text {
    margin-left: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    flex: 1;
  }
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #4285f4;
  color: #fff;
  border-radius: 8rpx;
  font-size: 24rpx;

  text {
    margin-left: 8rpx;
  }
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;

  &.required::after {
    content: "*";
    color: #ff4757;
    margin-left: 4rpx;
  }
}

.radio-group {
  display: flex;
  gap: 40rpx;
}

.area-row {
  display: flex;
  gap: 20rpx;
}

.product-item,
.terminal-item {
  padding: 20rpx 30rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.product-header,
.terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;

  text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }
}

.product-info,
.terminal-info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.popup-content {
  padding: 40rpx;
}

.popup-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 40rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}
</style>
