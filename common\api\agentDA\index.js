import { http } from '@/common/request/index.js'

// 模拟数据
const mockAgentList = [
  {
    id: '1',
    companyName: 'XXXCSO服务公司',
    province: '北京市',
    city: '北京市',
    phone: '15615884456',
    agentName: 'XXXCSO服务公司',
    agentCode: 'SHG1651651631G',
    agentType: 'enterprise',
    creditCode: 'HDHFH561616622',
    provinceName: '江苏',
    cityName: '南京',
    address: '江苏大道 124号',
    coverProvinceName: '江苏',
    coverCityName: '南京',
    teamMember: '曹玲',
    products: [
      {
        productName: '血糖仪 X1',
        agentExperience: 'enterprise',
        competitorExperience: 'enterprise',
        competitorName: '血糖仪A1'
      }
    ],
    terminals: [
      {
        terminalName: '第一人民医院',
        department: '内分泌科',
        agentExperience: '无'
      }
    ]
  },
  {
    id: '2',
    companyName: 'XXXCSO服务公司',
    province: '北京市',
    city: '北京市',
    phone: '15615884456',
    agentName: 'XXXCSO服务公司',
    agentCode: 'SHG1651651632G',
    agentType: 'individual',
    creditCode: 'HDHFH561616623',
    provinceName: '上海',
    cityName: '上海市',
    address: '上海大道 125号',
    coverProvinceName: '上海',
    coverCityName: '上海市',
    teamMember: '张三',
    products: [],
    terminals: []
  }
]

// 获取代理商列表
export const getAgentList = (params) => {
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      const { type, keyword, provinceCode, cityCode, pageNum = 1, pageSize = 10 } = params

      let filteredList = [...mockAgentList]

      // 根据类型过滤
      if (type === 'reserve') {
        // 储备代理商逻辑
      } else if (type === 'formal') {
        // 正式代理商逻辑
      }

      // 根据关键词过滤
      if (keyword) {
        filteredList = filteredList.filter(item =>
          item.companyName.includes(keyword) ||
          item.phone.includes(keyword)
        )
      }

      // 根据省市过滤
      if (provinceCode) {
        filteredList = filteredList.filter(item => item.provinceCode === provinceCode)
      }
      if (cityCode) {
        filteredList = filteredList.filter(item => item.cityCode === cityCode)
      }

      // 分页
      const start = (pageNum - 1) * pageSize
      const end = start + pageSize
      const list = filteredList.slice(start, end)

      resolve({
        code: 200,
        data: {
          list,
          total: filteredList.length
        },
        message: 'success'
      })
    }, 500)
  })
}

// 新增代理商
export const addAgent = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟新增成功
      resolve({
        code: 200,
        data: { id: Date.now().toString() },
        message: '新增成功'
      })
    }, 1000)
  })
}

// 编辑代理商
export const editAgent = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟编辑成功
      resolve({
        code: 200,
        data: null,
        message: '编辑成功'
      })
    }, 1000)
  })
}

// 删除代理商
export const deleteAgent = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟删除成功
      resolve({
        code: 200,
        data: null,
        message: '删除成功'
      })
    }, 500)
  })
}

// 获取代理商详情
export const getAgentDetail = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 根据ID返回对应的详情
      const agent = mockAgentList.find(item => item.id === id)
      if (agent) {
        resolve({
          code: 200,
          data: agent,
          message: 'success'
        })
      } else {
        resolve({
          code: 404,
          data: null,
          message: '代理商不存在'
        })
      }
    }, 500)
  })
}

// 获取意向产品列表
export const getProductList = (params) => {
  return http.get('/product/list', { params })
}

// 获取意向终端列表
export const getTerminalList = (params) => {
  return http.get('/terminal/list', { params })
}

// 获取团队人员列表
export const getTeamList = (params) => {
  return http.get('/team/list', { params })
}
